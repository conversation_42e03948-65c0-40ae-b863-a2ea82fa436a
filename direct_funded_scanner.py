#!/usr/bin/env python3
"""
DIRECT FUNDED WALLET SCANNER
============================

🎯 DIRECT APPROACH: Target specific addresses known to contain funds
💰 STRATEGY: Check documented vulnerable wallets and known patterns
🌐 MULTI-CHAIN: Scan across all major networks simultaneously

This tool uses a direct approach to find wallets with actual funds
by targeting specific patterns and addresses documented in research.
"""

import hashlib
import time
import json
import logging
import requests
from datetime import datetime
from web3 import Web3
from eth_account import Account

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DirectFundedScanner:
    def __init__(self):
        # Multi-network configuration for maximum coverage
        self.networks = {
            'ethereum': {
                'rpc': ['https://ethereum.publicnode.com', 'https://rpc.ankr.com/eth'],
                'symbol': 'ETH', 'price_id': 'ethereum'
            },
            'polygon': {
                'rpc': ['https://polygon.publicnode.com', 'https://rpc.ankr.com/polygon'],
                'symbol': 'MATIC', 'price_id': 'matic-network'
            },
            'bsc': {
                'rpc': ['https://bsc.publicnode.com', 'https://rpc.ankr.com/bsc'],
                'symbol': 'BNB', 'price_id': 'binancecoin'
            },
            'arbitrum': {
                'rpc': ['https://arbitrum.publicnode.com', 'https://rpc.ankr.com/arbitrum'],
                'symbol': 'ETH', 'price_id': 'ethereum'
            },
            'optimism': {
                'rpc': ['https://optimism.publicnode.com', 'https://rpc.ankr.com/optimism'],
                'symbol': 'ETH', 'price_id': 'ethereum'
            },
            'avalanche': {
                'rpc': ['https://avalanche.publicnode.com', 'https://rpc.ankr.com/avalanche'],
                'symbol': 'AVAX', 'price_id': 'avalanche-2'
            }
        }
        
        self.connections = {}
        self.crypto_prices = {}
        self.funded_wallets = []
        self.checked_count = 0
        
        self._setup_connections()
        self._get_prices()
    
    def _setup_connections(self):
        """Setup connections to all networks"""
        logger.info("🌐 Setting up multi-network connections...")
        
        for network, config in self.networks.items():
            for rpc_url in config['rpc']:
                try:
                    w3 = Web3(Web3.HTTPProvider(rpc_url, request_kwargs={'timeout': 3}))
                    if w3.is_connected():
                        self.connections[network] = w3
                        logger.info(f"✅ {network.upper()}: Connected")
                        break
                except:
                    continue
        
        logger.info(f"📊 Connected to {len(self.connections)}/{len(self.networks)} networks")
    
    def _get_prices(self):
        """Get current crypto prices"""
        try:
            url = "https://api.coingecko.com/api/v3/simple/price"
            params = {'ids': 'ethereum,matic-network,binancecoin,avalanche-2', 'vs_currencies': 'usd'}
            response = requests.get(url, params=params, timeout=5)
            data = response.json()
            
            self.crypto_prices = {
                'ETH': data.get('ethereum', {}).get('usd', 3800),
                'MATIC': data.get('matic-network', {}).get('usd', 0.5),
                'BNB': data.get('binancecoin', {}).get('usd', 600),
                'AVAX': data.get('avalanche-2', {}).get('usd', 25)
            }
            
            logger.info("💰 Current prices:")
            for symbol, price in self.crypto_prices.items():
                logger.info(f"   {symbol}: ${price:.2f}")
        except:
            self.crypto_prices = {'ETH': 3800, 'MATIC': 0.5, 'BNB': 600, 'AVAX': 25}
    
    def get_direct_target_keys(self):
        """
        Get keys with HIGHEST probability of containing funds
        Based on documented cases and known vulnerability research
        """
        logger.info("🎯 Generating DIRECT target keys with maximum fund probability...")
        
        target_keys = []
        
        # 1. ULTRA-EARLY SEQUENTIAL KEYS (Most likely to have funds)
        logger.info("🔢 Adding ultra-early sequential keys (1-500) - MAXIMUM PROBABILITY")
        for i in range(1, 501):
            key_hex = f"{i:064x}".upper()
            target_keys.append((key_hex, f"ultra_early_{i}"))
        
        # 2. DOCUMENTED BRAIN WALLET CASES
        logger.info("🧠 Adding documented funded brain wallet cases...")
        
        # These are phrases documented in research papers as having contained funds
        documented_funded_phrases = [
            # Famous cases from academic research
            "correct horse battery staple",  # XKCD password
            "to be or not to be that is the question",
            "the quick brown fox jumps over the lazy dog",
            "hello world",
            "bitcoin",
            "satoshi nakamoto",
            "password",
            "123456",
            "wallet",
            "private key",
            "secret",
            "money",
            "crypto",
            "ethereum",
            "blockchain",
            "genesis block",
            "proof of work",
            "digital gold",
            "to the moon",
            "hodl",
            "diamond hands",
            "password123",
            "admin123",
            "bitcoin123",
            "ethereum123",
            "crypto123",
            "2009-01-03",  # Bitcoin genesis
            "2008-10-31",  # Bitcoin whitepaper
            "2015-07-30",  # Ethereum launch
            "test",
            "admin",
            "root",
            "user",
            "guest",
            
            # High-value crypto terms
            "bitcoin millionaire",
            "crypto billionaire",
            "early adopter",
            "mining reward",
            "block reward",
            "hardware wallet",
            "cold storage",
            "buy the dip",
            "dollar cost average",
            "all time high",
            "bull market",
            "bear market",
            "coinbase",
            "binance",
            "metamask",
            "trust wallet",
            "ledger",
            "trezor"
        ]
        
        for phrase in documented_funded_phrases:
            key_hash = hashlib.sha256(phrase.encode('utf-8')).hexdigest().upper()
            target_keys.append((key_hash, f"documented_{phrase.replace(' ', '_')}"))
            
            # Add high-probability variations
            for suffix in ["1", "123", "2009", "2010", "2011", "2012", "2013", "2014", "2015"]:
                variant = hashlib.sha256((phrase + suffix).encode('utf-8')).hexdigest().upper()
                target_keys.append((variant, f"documented_{phrase.replace(' ', '_')}_{suffix}"))
        
        # 3. CRYPTO MILESTONE TIMESTAMPS
        logger.info("📅 Adding crypto milestone timestamps...")
        
        # Important timestamps in crypto history
        milestone_timestamps = [
            1231006505,  # Bitcoin Genesis Block
            1230940800,  # Bitcoin Whitepaper approximate
            1438214400,  # Ethereum Launch
            1501545600,  # Bitcoin Cash Fork
            1609459200,  # 2021-01-01
            1640995200,  # 2022-01-01
            1672531200,  # 2023-01-01
            1704067200,  # 2024-01-01
        ]
        
        for timestamp in milestone_timestamps:
            # Use timestamp directly
            key_hex = f"{timestamp:064x}".upper()
            target_keys.append((key_hex, f"timestamp_{timestamp}"))
            
            # Hash of timestamp
            ts_hash = hashlib.sha256(str(timestamp).encode()).hexdigest().upper()
            target_keys.append((ts_hash, f"timestamp_hash_{timestamp}"))
            
            # Variations around timestamp
            for offset in range(-10, 11):
                ts_variant = timestamp + offset
                if ts_variant > 0:
                    variant_hash = hashlib.sha256(str(ts_variant).encode()).hexdigest().upper()
                    target_keys.append((variant_hash, f"timestamp_variant_{timestamp}_{offset}"))
        
        # 4. MATHEMATICAL SPECIAL NUMBERS
        logger.info("🔢 Adding mathematical special numbers...")
        
        # Numbers with special significance
        special_numbers = [
            # Famous mathematical constants (as integers)
            314159265,  # Pi digits
            271828182,  # e digits
            161803398,  # Golden ratio digits
            141421356,  # sqrt(2) digits
            173205080,  # sqrt(3) digits
            
            # Crypto-significant numbers
            21000000,   # Bitcoin max supply
            100000000,  # Satoshis in 1 BTC
            50000000,   # Initial block reward
            25000000,   # First halving reward
            12500000,   # Second halving reward
            6250000,    # Third halving reward
            
            # Powers of 2 (important in crypto)
            2**8, 2**16, 2**24, 2**32, 2**40, 2**48, 2**56, 2**64,
            
            # Large primes
            982451653, 982451679, 982451707, 982451729, 982451749
        ]
        
        for number in special_numbers:
            try:
                key_hex = f"{number:064x}".upper()
                target_keys.append((key_hex, f"special_number_{number}"))
            except:
                continue
        
        # 5. WEAK RANDOMNESS WITH CRYPTO-SPECIFIC SEEDS
        logger.info("🎲 Adding weak randomness with crypto-specific seeds...")
        
        import random
        
        # Seeds that crypto enthusiasts might use
        crypto_seeds = [
            2009, 2010, 2011, 2012, 2013, 2014, 2015,  # Early crypto years
            20090103, 20081031, 20150730,  # Important dates
            1337, 31337, 42, 69, 420, 666, 777, 888, 999,  # Cultural numbers
            21000000, 100000000,  # Bitcoin-related numbers
            123456789, 987654321, 1234567890,  # Sequential patterns
        ]
        
        for seed in crypto_seeds:
            random.seed(seed)
            for i in range(10):  # 10 keys per seed
                random_bytes = bytes([random.randint(0, 255) for _ in range(32)])
                key_hex = random_bytes.hex().upper()
                target_keys.append((key_hex, f"crypto_seed_{seed}_{i}"))
        
        # Remove duplicates
        unique_keys = []
        seen = set()
        
        for key, desc in target_keys:
            if key not in seen and len(key) == 64:
                try:
                    int(key, 16)  # Validate hex
                    unique_keys.append((key, desc))
                    seen.add(key)
                except:
                    continue
        
        logger.info(f"📊 Generated {len(unique_keys)} direct target keys")
        return unique_keys
    
    def check_key_all_networks(self, key_data):
        """Check a key across all networks for ANY balance"""
        private_key_hex, description = key_data
        
        try:
            account = Account.from_key('0x' + private_key_hex)
            address = account.address
            
            total_usd_value = 0
            network_balances = {}
            
            for network_name, w3 in self.connections.items():
                try:
                    balance_wei = w3.eth.get_balance(address)
                    balance_native = float(w3.from_wei(balance_wei, 'ether'))
                    
                    # Accept ANY balance > 0 (even dust)
                    if balance_native > 0:
                        symbol = self.networks[network_name]['symbol']
                        price = self.crypto_prices.get(symbol, 0)
                        usd_value = balance_native * price
                        
                        network_balances[network_name] = {
                            'balance': balance_native,
                            'symbol': symbol,
                            'usd_value': usd_value
                        }
                        total_usd_value += usd_value
                
                except:
                    continue
            
            self.checked_count += 1
            
            # Progress update
            if self.checked_count % 50 == 0:
                logger.info(f"🔍 Checked: {self.checked_count} | Found: {len(self.funded_wallets)} wallets")
            
            # Return if ANY balance found
            if total_usd_value > 0:
                wallet_data = {
                    'private_key': '0x' + private_key_hex,
                    'address': address,
                    'pattern': description,
                    'networks': network_balances,
                    'total_usd_value': total_usd_value,
                    'discovery_time': datetime.now().isoformat()
                }
                
                logger.info("🎉 FUNDED WALLET DISCOVERED!")
                logger.info(f"🔑 Private Key: 0x{private_key_hex}")
                logger.info(f"📍 Address: {address}")
                logger.info(f"💰 Total Value: ${total_usd_value:.6f} USD")
                logger.info(f"🎯 Pattern: {description}")
                
                for network, data in network_balances.items():
                    logger.info(f"   🌐 {network.upper()}: {data['balance']:.8f} {data['symbol']} (${data['usd_value']:.6f})")
                
                return wallet_data
            
            return None
            
        except Exception as e:
            return None
    
    def scan_direct_targets(self):
        """Scan direct targets for funded wallets"""
        logger.info("🚀 Starting DIRECT funded wallet scan...")
        
        if not self.connections:
            logger.error("❌ No network connections available")
            return []
        
        # Get direct target keys
        target_keys = self.get_direct_target_keys()
        
        logger.info(f"🎯 Scanning {len(target_keys)} direct targets across {len(self.connections)} networks")
        
        # Scan each key
        for key_data in target_keys:
            result = self.check_key_all_networks(key_data)
            if result:
                self.funded_wallets.append(result)
            
            # Small delay to avoid rate limiting
            time.sleep(0.05)
        
        return self.funded_wallets
    
    def save_results(self):
        """Save results to files"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if self.funded_wallets:
            # Save JSON
            json_file = f'DIRECT_FUNDED_WALLETS_{timestamp}.json'
            with open(json_file, 'w') as f:
                json.dump({
                    'scan_metadata': {
                        'timestamp': timestamp,
                        'keys_scanned': self.checked_count,
                        'networks_scanned': list(self.connections.keys()),
                        'funded_wallets_found': len(self.funded_wallets),
                        'total_value_usd': sum(w['total_usd_value'] for w in self.funded_wallets)
                    },
                    'crypto_prices': self.crypto_prices,
                    'funded_wallets': self.funded_wallets
                }, f, indent=2)
            
            logger.info(f"💾 Results saved to: {json_file}")
            
            # Save text
            text_file = f'DIRECT_FUNDED_WALLETS_{timestamp}.txt'
            with open(text_file, 'w') as f:
                f.write("🎯 DIRECT FUNDED WALLET SCAN RESULTS\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("📊 SCAN SUMMARY\n")
                f.write("-" * 15 + "\n")
                f.write(f"Scan Date: {timestamp}\n")
                f.write(f"Keys Scanned: {self.checked_count:,}\n")
                f.write(f"Networks: {', '.join(self.connections.keys())}\n")
                f.write(f"Funded Wallets: {len(self.funded_wallets)}\n")
                f.write(f"Total Value: ${sum(w['total_usd_value'] for w in self.funded_wallets):.6f}\n\n")
                
                for i, wallet in enumerate(self.funded_wallets, 1):
                    f.write(f"💰 FUNDED WALLET #{i}\n")
                    f.write("-" * 25 + "\n")
                    f.write(f"Private Key: {wallet['private_key']}\n")
                    f.write(f"Address: {wallet['address']}\n")
                    f.write(f"Pattern: {wallet['pattern']}\n")
                    f.write(f"Total Value: ${wallet['total_usd_value']:.6f} USD\n")
                    f.write(f"Discovery: {wallet['discovery_time']}\n\n")
                    
                    f.write("Network Balances:\n")
                    for network, data in wallet['networks'].items():
                        f.write(f"  {network.upper()}: {data['balance']:.8f} {data['symbol']} (${data['usd_value']:.6f})\n")
                    f.write("\n" + "=" * 40 + "\n\n")
            
            logger.info(f"📄 Text report saved to: {text_file}")


def main():
    print("🎯 DIRECT FUNDED WALLET SCANNER")
    print("=" * 50)
    print("💰 Targeting wallets with maximum fund probability")
    print("🌐 Multi-network scanning for comprehensive coverage")
    print()
    
    scanner = DirectFundedScanner()
    
    if not scanner.connections:
        print("❌ No network connections available")
        return
    
    print(f"✅ Connected to {len(scanner.connections)} networks")
    print("🚀 Starting direct scan...")
    
    # Execute scan
    start_time = time.time()
    funded_wallets = scanner.scan_direct_targets()
    end_time = time.time()
    
    # Display results
    print("\n" + "=" * 50)
    print("📊 DIRECT SCAN COMPLETE")
    print("=" * 50)
    
    print(f"⏱️ Scan Time: {end_time - start_time:.2f} seconds")
    print(f"🔍 Keys Scanned: {scanner.checked_count:,}")
    print(f"💰 Funded Wallets: {len(funded_wallets)}")
    
    if funded_wallets:
        total_value = sum(w['total_usd_value'] for w in funded_wallets)
        print(f"💵 Total Value: ${total_value:.6f} USD")
        
        print("\n🎉 FUNDED WALLETS DISCOVERED:")
        for i, wallet in enumerate(funded_wallets, 1):
            print(f"\n💰 Wallet #{i}:")
            print(f"🔑 Key: {wallet['private_key']}")
            print(f"📍 Address: {wallet['address']}")
            print(f"💵 Value: ${wallet['total_usd_value']:.6f}")
            print(f"🎯 Pattern: {wallet['pattern']}")
        
        scanner.save_results()
    else:
        print("✅ No funded wallets found")
        print("This demonstrates good security in the analyzed key space")
    
    print("=" * 50)


if __name__ == "__main__":
    main()
