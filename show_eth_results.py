#!/usr/bin/env python3
"""
Display Ethereum Wallet Scan Results
"""

import json
import os
from datetime import datetime

def show_scan_results():
    print("=" * 60)
    print("🚀 ETHEREUM WALLET SCANNER RESULTS")
    print("=" * 60)
    
    # Find the most recent results files
    result_files = []
    for file in os.listdir('.'):
        if file.startswith('DIRECT_FUNDED_WALLETS_') and file.endswith('.json'):
            result_files.append(file)
        elif file.startswith('KNOWN_FUNDED_WALLETS_') and file.endswith('.json'):
            result_files.append(file)
    
    result_files.sort(reverse=True)  # Most recent first
    
    if not result_files:
        print("❌ No scan results found")
        return
    
    for file in result_files[:2]:  # Show top 2 most recent
        print(f"\n📊 Results from: {file}")
        print("-" * 50)
        
        try:
            with open(file, 'r') as f:
                data = json.load(f)
            
            # Show metadata
            if 'scan_metadata' in data:
                meta = data['scan_metadata']
                print(f"🕒 Scan Time: {meta['timestamp']}")
                print(f"🔍 Keys Scanned: {meta.get('keys_scanned', 'N/A'):,}")
                print(f"🌐 Networks: {', '.join(meta.get('networks_scanned', []))}")
                print(f"💰 Funded Wallets Found: {meta.get('funded_wallets_found', 0):,}")
                print(f"💵 Total Value: ${meta.get('total_value_usd', 0):.6f} USD")
            elif 'analysis_metadata' in data:
                meta = data['analysis_metadata']
                print(f"🕒 Analysis Time: {meta['timestamp']}")
                print(f"🔍 Keys Checked: {meta.get('keys_checked', 'N/A'):,}")
                print(f"💰 Wallets Found: {meta.get('wallets_found', 0):,}")
                print(f"💵 Total ETH: {meta.get('total_eth', 0):.12f}")
                print(f"💵 Total USD: ${meta.get('total_usd', 0):.6f}")
            
            # Show first few funded wallets
            wallets = data.get('funded_wallets', [])
            if wallets:
                print(f"\n🎉 Sample Funded Wallets (showing first 5 of {len(wallets)}):")
                for i, wallet in enumerate(wallets[:5], 1):
                    print(f"\n💰 Wallet #{i}:")
                    print(f"   🔑 Private Key: {wallet.get('private_key', 'N/A')}")
                    print(f"   📍 Address: {wallet.get('address', 'N/A')}")
                    
                    if 'networks' in wallet:
                        total_value = 0
                        for network, data in wallet['networks'].items():
                            balance = data.get('balance', 0)
                            symbol = data.get('symbol', '')
                            usd_value = data.get('usd_value', 0)
                            if balance > 0:
                                print(f"   💎 {network.upper()}: {balance:.12f} {symbol} (${usd_value:.6f})")
                                total_value += usd_value
                        print(f"   💵 Total Value: ${total_value:.6f} USD")
                    else:
                        balance = wallet.get('balance_eth', 0)
                        usd_value = wallet.get('usd_value', 0)
                        print(f"   💎 Balance: {balance:.12f} ETH")
                        print(f"   💵 USD Value: ${usd_value:.6f}")
                    
                    print(f"   🎯 Pattern: {wallet.get('pattern', 'N/A')}")
            
        except Exception as e:
            print(f"❌ Error reading {file}: {e}")
    
    print("\n" + "=" * 60)
    print("✅ SCAN RESULTS SUMMARY COMPLETE")
    print("=" * 60)

def run_quick_demo():
    print("\n🔍 Running Quick Ethereum Connection Test...")
    
    try:
        from web3 import Web3
        from eth_account import Account
        
        # Test connection
        rpc_url = 'https://ethereum.publicnode.com'
        w3 = Web3(Web3.HTTPProvider(rpc_url, request_kwargs={'timeout': 10}))
        
        if w3.is_connected():
            latest_block = w3.eth.block_number
            print(f"✅ Connected to Ethereum mainnet")
            print(f"📊 Latest block: {latest_block:,}")
            
            # Test a few simple keys
            print(f"\n🎯 Testing first 3 sequential keys...")
            for i in range(1, 4):
                key_hex = f"{i:064x}"
                account = Account.from_key('0x' + key_hex)
                address = account.address
                
                balance_wei = w3.eth.get_balance(address)
                balance_eth = float(w3.from_wei(balance_wei, 'ether'))
                
                status = "💰 FUNDED!" if balance_eth > 0 else "💸 Empty"
                print(f"   Key {i}: {address} - {balance_eth:.12f} ETH {status}")
        else:
            print("❌ Failed to connect to Ethereum")
            
    except Exception as e:
        print(f"❌ Demo error: {e}")

if __name__ == "__main__":
    show_scan_results()
    run_quick_demo()
