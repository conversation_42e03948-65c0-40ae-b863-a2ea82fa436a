#!/usr/bin/env python3
"""
Test Ethereum Connection and Run Basic Wallet Scan
"""

from web3 import Web3
from eth_account import Account
import time

def test_connection():
    print("🌐 Testing Ethereum connection...")
    
    rpc_urls = [
        'https://ethereum.publicnode.com',
        'https://rpc.ankr.com/eth',
        'https://eth.llamarpc.com'
    ]
    
    for url in rpc_urls:
        try:
            print(f"Trying {url}...")
            w3 = Web3(Web3.HTTPProvider(url, request_kwargs={'timeout': 10}))
            if w3.is_connected():
                latest_block = w3.eth.block_number
                print(f"✅ Connected to {url}")
                print(f"📊 Latest block: {latest_block}")
                return w3
        except Exception as e:
            print(f"❌ Failed: {e}")
            continue
    
    print("❌ No connection available")
    return None

def test_simple_wallet():
    print("\n🔍 Testing simple wallet generation...")
    
    # Test with a simple key
    test_key = "0000000000000000000000000000000000000000000000000000000000000001"
    try:
        account = Account.from_key('0x' + test_key)
        print(f"✅ Generated address: {account.address}")
        return True
    except Exception as e:
        print(f"❌ Wallet generation failed: {e}")
        return False

def main():
    print("=" * 50)
    print("🚀 ETHEREUM WALLET SCANNER TEST")
    print("=" * 50)
    
    # Test connection
    w3 = test_connection()
    if not w3:
        return
    
    # Test wallet generation
    if not test_simple_wallet():
        return
    
    print("\n🎯 Running basic scan of first 10 keys...")
    
    for i in range(1, 11):
        try:
            key_hex = f"{i:064x}"
            account = Account.from_key('0x' + key_hex)
            address = account.address
            
            # Check balance
            balance_wei = w3.eth.get_balance(address)
            balance_eth = float(w3.from_wei(balance_wei, 'ether'))
            
            print(f"Key {i:2d}: {address} - {balance_eth:.8f} ETH")
            
            if balance_eth > 0:
                print(f"🎉 FOUND FUNDED WALLET! Balance: {balance_eth} ETH")
            
            time.sleep(0.1)  # Rate limiting
            
        except Exception as e:
            print(f"Error checking key {i}: {e}")
    
    print("\n✅ Test scan complete!")

if __name__ == "__main__":
    main()
